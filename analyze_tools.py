#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯工具分析脚本 - 基于文件名和已知信息分析腾讯公益数字化工具
"""

import os
import json

def analyze_tencent_tools():
    """分析腾讯工具截图文件，提取工具信息"""
    
    # 获取所有PNG截图文件
    png_files = [f for f in os.listdir('.') if f.startswith('FireShot Capture') and f.endswith('.png')]
    
    tools_info = {}
    
    for file in png_files:
        # 从文件名提取工具信息
        if '腾讯频道的运营应用介绍' in file:
            tools_info['腾讯频道运营工具'] = {
                'file_name': file,
                'tool_type': '私域运营工具',
                'main_functions': [
                    '频道创建和管理',
                    '内容发布和互动',
                    '用户社群运营',
                    '数据统计分析'
                ],
                'business_value': [
                    '提升用户粘性和参与度',
                    '建立机构品牌影响力',
                    '增强捐赠人归属感',
                    '提供数据驱动的运营决策'
                ],
                'usage_scenarios': [
                    '项目进展分享',
                    '受助者故事传播',
                    '志愿者活动组织',
                    '捐赠人互动交流'
                ]
            }
        
        elif '企微私域运营工具使用手册' in file:
            tools_info['企业微信私域运营工具'] = {
                'file_name': file,
                'tool_type': '私域运营工具',
                'main_functions': [
                    '企业微信群管理',
                    '用户标签化管理',
                    '自动化消息推送',
                    '用户行为数据分析'
                ],
                'business_value': [
                    '精准用户触达',
                    '提升转化率',
                    '降低运营成本',
                    '增强用户体验'
                ],
                'usage_scenarios': [
                    '捐赠人维护',
                    '志愿者管理',
                    '项目宣传推广',
                    '用户服务支持'
                ]
            }
        
        elif '数字化工具【物资】方案介绍' in file:
            tools_info['物资执行数字化工具'] = {
                'file_name': file,
                'tool_type': '项目执行工具',
                'main_functions': [
                    '物资入库管理',
                    '仓储配送服务',
                    '物流跟踪',
                    '成本核算'
                ],
                'business_value': [
                    '提升物资发放效率',
                    '降低物流成本',
                    '确保物资安全到达',
                    '提供透明的执行记录'
                ],
                'usage_scenarios': [
                    '教育用品发放',
                    '生活物资配送',
                    '应急救援物资',
                    '节日慰问品发放'
                ]
            }
        
        elif '数字化工具【服务】方案介绍' in file:
            tools_info['服务执行数字化工具'] = {
                'file_name': file,
                'tool_type': '项目执行工具',
                'main_functions': [
                    '服务任务管理',
                    '受助对象核销',
                    '服务质量评估',
                    '执行数据统计'
                ],
                'business_value': [
                    '规范服务执行流程',
                    '提升服务质量',
                    '防止重复服务',
                    '量化服务成果'
                ],
                'usage_scenarios': [
                    '教育培训服务',
                    '心理咨询服务',
                    '技能培训课程',
                    '健康检查服务'
                ]
            }
        
        elif '腾讯频道的运营方案及全流程手册' in file:
            tools_info['腾讯频道全流程运营'] = {
                'file_name': file,
                'tool_type': '运营指导工具',
                'main_functions': [
                    '运营策略制定',
                    '内容规划指导',
                    '用户增长方案',
                    '效果评估体系'
                ],
                'business_value': [
                    '系统化运营指导',
                    '提升运营效果',
                    '标准化操作流程',
                    '持续优化改进'
                ],
                'usage_scenarios': [
                    '新频道启动',
                    '运营策略调整',
                    '团队培训指导',
                    '效果复盘分析'
                ]
            }
    
    return tools_info

def generate_funding_calculation_summary():
    """生成资助金额计算方法总结（排除资金拨付工具）"""

    funding_methods = {
        # 注意：资金拨付工具不使用，故不包含在资助范围内
        
        '公益物资执行数字化资助': {
            'max_amount': '8万元',
            'calculation_formula': '实际物流费用（以发票和结算单为准）',
            'requirements': [
                '使用物资执行工具',
                '物资须通过平台入仓并配送',
                '上门取件模式不纳入资助范围',
                '机构内部物品物流费用不纳入'
            ],
            'verification': '提供发票和仓储物流结算明细单'
        },
        
        '公益服务执行数字化资助': {
            'max_amount': '5万元',
            'calculation_formula': '资助系数 × 有效执行受助对象数量 × 5元',
            'coefficient_formula': '服务执行场景系数 + 人群系数 + 服务落地场景系数',
            'example': '课堂类，群体对象40人，线上场景：(1+0.5+0.1) × 40 × 5 = 320元',
            'requirements': [
                '服务对象去重，不重复计算',
                '须提交完整执行报告'
            ],
            'verification': '结合服务执行工具数据比对验收'
        },
        
        '公益机构私域及频道运营资助': {
            'max_amount': '2.4万元',
            'calculation_formula': '运营人员费用，不超过4000元/月',
            'requirements': [
                '月度数据均达标',
                '配置运营人员（可兼任）',
                '提供发薪或补贴转账记录'
            ],
            'verification': '以实际发薪金额为准'
        }
    }
    
    return funding_methods

def main():
    """主函数"""
    print("开始分析腾讯公益数字化工具...")
    
    # 分析工具信息
    tools_info = analyze_tencent_tools()
    
    print(f"\n=== 发现 {len(tools_info)} 个腾讯工具 ===")
    for tool_name, info in tools_info.items():
        print(f"\n【{tool_name}】")
        print(f"  文件: {info['file_name']}")
        print(f"  类型: {info['tool_type']}")
        print(f"  主要功能: {', '.join(info['main_functions'][:3])}...")
    
    # 生成资助计算方法总结
    funding_methods = generate_funding_calculation_summary()
    
    print(f"\n=== 资助金额计算方法总结（实际可用工具）===")
    total_max = 0
    for method_name, details in funding_methods.items():
        max_amount = float(details['max_amount'].replace('万元', ''))
        total_max += max_amount
        print(f"\n【{method_name}】")
        print(f"  最高资助: {details['max_amount']}")
        print(f"  计算公式: {details['calculation_formula']}")

    print(f"\n实际可获得最高资助金额: {total_max}万元")
    print("注意：资金拨付工具不使用，故不包含其2.5万元资助")
    
    # 保存分析结果
    analysis_result = {
        'tools_info': tools_info,
        'funding_methods': funding_methods,
        'summary': {
            'total_tools': len(tools_info),
            'max_total_funding': f"{total_max}万元",
            'excluded_funding': '2.5万元（资金拨付工具不使用）',
            'actual_max_funding': f"{total_max}万元",
            'execution_period': '2025年3月1日0:00至2025年8月31日24:00'
        }
    }
    
    with open('tencent_tools_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析完成！详细结果已保存到 tencent_tools_analysis.json")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档分析脚本 - 用于分析腾讯公益数字化资助项目相关文档
"""

import os
from docx import Document
from pptx import Presentation
import json

def analyze_donation_agreement(file_path):
    """分析捐赠协议文档，提取资助金额计算方法"""
    print(f"正在分析捐赠协议文档: {file_path}")
    
    try:
        doc = Document(file_path)
        content = []
        
        # 提取所有段落文本
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        # 查找附件1相关内容
        appendix_content = []
        found_appendix = False
        
        for i, text in enumerate(content):
            if "附件" in text and ("1" in text or "一" in text):
                found_appendix = True
                appendix_content.append(f"第{i+1}段: {text}")
            elif found_appendix and ("资助" in text or "计算" in text or "金额" in text or "使用数据" in text):
                appendix_content.append(f"第{i+1}段: {text}")
        
        # 查找资助金额计算相关内容
        funding_calculation = []
        for i, text in enumerate(content):
            if any(keyword in text for keyword in ["资助金额", "计算方法", "使用数据", "工具使用", "资助标准"]):
                funding_calculation.append(f"第{i+1}段: {text}")
        
        return {
            "total_paragraphs": len(content),
            "appendix_content": appendix_content,
            "funding_calculation": funding_calculation,
            "full_content": content[:50]  # 前50段用于概览
        }
        
    except Exception as e:
        print(f"分析捐赠协议时出错: {e}")
        return {"error": str(e)}

def analyze_pptx_presentation(file_path):
    """分析PowerPoint演示文稿"""
    print(f"正在分析PPT文档: {file_path}")
    
    try:
        prs = Presentation(file_path)
        slides_content = []
        
        for i, slide in enumerate(prs.slides):
            slide_text = []
            
            # 提取幻灯片中的文本
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    slide_text.append(shape.text.strip())
            
            if slide_text:
                slides_content.append({
                    "slide_number": i + 1,
                    "content": slide_text
                })
        
        return {
            "total_slides": len(prs.slides),
            "slides_with_content": len(slides_content),
            "slides_content": slides_content
        }
        
    except Exception as e:
        print(f"分析PPT时出错: {e}")
        return {"error": str(e)}

def main():
    """主函数"""
    print("开始分析腾讯公益数字化资助项目文档...")
    
    # 分析捐赠协议
    donation_agreement_path = "【捐赠协议】海南亚洲公益研究院捐赠协议（公募）.docx"
    if os.path.exists(donation_agreement_path):
        agreement_analysis = analyze_donation_agreement(donation_agreement_path)
        
        print("\n=== 捐赠协议分析结果 ===")
        print(f"总段落数: {agreement_analysis.get('total_paragraphs', 0)}")
        
        if agreement_analysis.get('appendix_content'):
            print("\n附件1相关内容:")
            for content in agreement_analysis['appendix_content']:
                print(f"  {content}")
        
        if agreement_analysis.get('funding_calculation'):
            print("\n资助金额计算相关内容:")
            for content in agreement_analysis['funding_calculation']:
                print(f"  {content}")
        
        # 保存详细分析结果
        with open('donation_agreement_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(agreement_analysis, f, ensure_ascii=False, indent=2)
    
    # 分析PPT文档
    ppt_path = "捐赠人服务产品运营指引（更新于2025.02）.pptx"
    if os.path.exists(ppt_path):
        ppt_analysis = analyze_pptx_presentation(ppt_path)
        
        print(f"\n=== PPT文档分析结果 ===")
        print(f"总幻灯片数: {ppt_analysis.get('total_slides', 0)}")
        print(f"包含内容的幻灯片数: {ppt_analysis.get('slides_with_content', 0)}")
        
        if ppt_analysis.get('slides_content'):
            print("\n幻灯片内容概览:")
            for slide in ppt_analysis['slides_content'][:10]:  # 显示前10张幻灯片
                print(f"  第{slide['slide_number']}张幻灯片:")
                for content in slide['content'][:3]:  # 每张幻灯片显示前3个文本块
                    print(f"    - {content[:100]}...")
        
        # 保存详细分析结果
        with open('ppt_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(ppt_analysis, f, ensure_ascii=False, indent=2)
    
    print("\n文档分析完成！详细结果已保存到JSON文件中。")

if __name__ == "__main__":
    main()
